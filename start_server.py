#!/usr/bin/env python3
"""
道路生长动画演示服务器
快速启动HTTP服务器来运行道路施工进度可视化系统
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

def main():
    # 设置端口
    PORT = 8080
    
    # 检查必要文件
    required_files = ['index.html', 'app.js', 'gsdx1.glb']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        print("\n请确保所有文件都在当前目录中。")
        return
    
    # 创建HTTP服务器
    Handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print("🚧 道路生长动画演示服务器")
            print("=" * 50)
            print(f"✅ 服务器启动成功")
            print(f"🌐 访问地址: http://localhost:{PORT}")
            print(f"📁 服务目录: {os.getcwd()}")
            print()
            print("📋 可用页面:")
            print(f"   • 主演示: http://localhost:{PORT}/index.html")
            print(f"   • 测试页面: http://localhost:{PORT}/test_growth.html")
            print(f"   • 说明文档: http://localhost:{PORT}/README_生长动画.md")
            print()
            print("🎮 新功能特性:")
            print("   • 几何体生长动画")
            print("   • 施工粒子效果")
            print("   • 施工设备跟随")
            print("   • 可变动画速度")
            print("   • 效果开关控制")
            print()
            print("⚠️  注意事项:")
            print("   • 确保GLB模型文件存在")
            print("   • 建议使用现代浏览器")
            print("   • 启用所有效果可能影响性能")
            print()
            print("🔧 控制台调试命令:")
            print("   roadManager.growthAnimationEnabled = true")
            print("   roadManager.animationSpeed = 2.0")
            print("   roadManager.showRoadPositions()")
            print()
            print("按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}/index.html')
                print("🌐 已自动打开浏览器")
            except:
                print("⚠️  请手动打开浏览器访问上述地址")
            
            print()
            httpd.serve_forever()
            
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用")
            print("请尝试以下解决方案:")
            print("1. 关闭其他使用该端口的程序")
            print("2. 修改脚本中的PORT变量使用其他端口")
            print("3. 或者直接在浏览器中打开 index.html 文件")
        else:
            print(f"❌ 启动服务器时出错: {e}")
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

if __name__ == "__main__":
    main()
