# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Structure

This is a road and bridge engineering progress management system built with Three.js:
- `gsdx1.glb` - 3D GLB model file containing the road/bridge project (~97MB)
- `index.html` - Main HTML page with UI and styling
- `app.js` - Three.js application with progress management logic

## Development Commands

### Running the Application
```bash
# Required: Serve files with HTTP server (GLB loading requires HTTP protocol)
python -m http.server 8000
# Or use any HTTP server like Live Server extension
# Access via http://localhost:8000
```

### No Build/Test Commands
This is a client-side only application with no build process or testing framework configured.

## Application Architecture

### Core Class Structure
- **RoadProgressManager**: Central application class managing all functionality
  - Scene initialization (`initThreeJS()`, `setupLighting()`)
  - Model loading and analysis (`loadModel()`, `analyzeRoadComponents()`)
  - Progress simulation (`setupProgressSimulation()`, `updateProgress()`)
  - Camera management with follow mode (`updateCameraFollow()`, `smoothMoveCameraTo()`)
  - Road height adjustment system (`updateRoadHeight()`, `adjustRoadToTerrain()`)

### Key Technical Systems

#### Model Analysis and Segmentation
- **Smart Component Detection**: Automatically identifies road vs terrain meshes by name patterns and geometry position
- **Length-based Segmentation**: Divides roads into 8 sequential construction segments based on spatial distribution
- **Terrain-aware Positioning**: Uses raycasting to position roads correctly on terrain surface

#### Progress Visualization Pipeline
1. **Timeline to Progress**: Converts timeline position (0-100%) to current construction month
2. **Segment Status Calculation**: Determines each segment's status (not-started/in-progress/completed) 
3. **Progressive Loading System**: Road segments appear gradually based on construction progress:
   - 0-10% progress: Road segment completely hidden
   - 10-30% progress: Road segment gradually appears (opacity 0.3→1.0) 
   - 30%+ progress: Road segment fully visible with construction materials
   - 100% progress: Road segment uses completion materials
4. **Material Updates**: Applies color coding with dynamic emissive effects for active construction
5. **Synchronized Visibility**: Road loading matches actual construction timeline

#### Camera Follow System  
- **Construction Front Tracking**: Calculates frontmost construction position dynamically
- **Smooth Camera Movement**: Uses easing functions for natural camera transitions
- **Terrain-aware Positioning**: Maintains appropriate height above terrain surface
- **Multiple View Presets**: Close, medium, far, and aerial camera configurations

### Dependencies
- Three.js r128 (CDN): Core 3D rendering
- GLTFLoader: .glb model file loading  
- OrbitControls: 3D scene navigation

### Debug and Development Tools

#### Console Commands Available
```javascript
// Camera control presets
roadManager.setCameraPreset("close|medium|far|aerial")

// Road height debugging
roadManager.showRoadPositions()        // Display current road positions
roadManager.adjustRoadHeight(offset)   // Adjust height by offset
roadManager.setRoadAbsoluteHeight(y)   // Set absolute Y position

// Access current state
roadManager.roadHeightOffset          // Current height adjustment
roadManager.roadSegments             // All road segment data
```

#### Key Customization Points
- `projectDuration`: Total construction timeline (default: 24 months)
- `createRoadSegmentsByLength()`: Road segmentation algorithm
- `analyzeRoadComponents()`: Component identification logic  
- `cameraFollowHeight/Distance`: Follow mode camera positioning
- Road segment timing in `startMonth` and `duration` properties