@echo off
chcp 65001 >nul
title 道路生长动画演示服务器

echo.
echo 🚧 道路生长动画演示服务器
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python 3.x
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要文件
if not exist "index.html" (
    echo ❌ 缺少文件: index.html
    goto :missing_files
)

if not exist "app.js" (
    echo ❌ 缺少文件: app.js
    goto :missing_files
)

if not exist "gsdx1.glb" (
    echo ❌ 缺少文件: gsdx1.glb
    goto :missing_files
)

echo ✅ 所有必要文件检查完成
echo.

REM 启动Python HTTP服务器
echo 🚀 正在启动服务器...
echo.
python start_server.py

goto :end

:missing_files
echo.
echo 请确保以下文件都在当前目录中:
echo   • index.html - 主页面文件
echo   • app.js - JavaScript应用文件  
echo   • gsdx1.glb - 3D模型文件
echo.
pause
exit /b 1

:end
pause
