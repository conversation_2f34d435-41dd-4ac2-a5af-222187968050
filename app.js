class RoadProgressManager {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.model = null;
        this.roadSegments = [];
        this.currentProgress = 0;
        this.isPlaying = false;
        this.playInterval = null;

        // 工程时间轴配置 (24个月项目周期)
        this.projectDuration = 24; // 月
        this.startDate = new Date('2024-01-01');

        // 摄像机跟随配置
        this.followMode = true; // 是否启用跟随模式
        this.originalCameraPosition = null;
        this.originalCameraTarget = null;
        this.cameraFollowHeight = 6; // 摄像机跟随高度 - 降低以贴近道路
        this.cameraFollowDistance = 12; // 摄像机跟随距离 - 减小以更接近
        this.cameraBackwardOffset = 0.8; // 后退偏移比例
        this.cameraSideOffset = 0.6; // 侧向偏移比例

        // 道路高度调节
        this.roadHeightOffset = 0; // 道路高度偏移量

        // 增强的生长动画配置
        this.growthAnimationEnabled = true; // 启用生长动画
        this.segmentSubdivisions = 20; // 每个段落的细分数量
        this.constructionEquipment = []; // 施工设备数组
        this.particleSystems = []; // 粒子系统数组
        this.roadCurves = []; // 道路曲线数据
        this.animationSpeed = 1.0; // 动画速度倍数

        // 施工效果配置
        this.showConstructionEffects = true; // 显示施工效果
        this.showParticles = true; // 显示粒子效果
        this.showEquipment = true; // 显示施工设备

        this.init();
    }
    
    init() {
        this.initThreeJS();
        this.loadModel();
        this.setupEventListeners();
        this.setupProgressSimulation();
    }
    
    initThreeJS() {
        const container = document.getElementById('canvas-container');
        
        // 创建场景
        this.scene = new THREE.Scene();
        this.scene.background = new THREE.Color(0x1a1a1a);
        
        // 创建相机 - 支持0-10000距离范围
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            10000
        );
        this.camera.position.set(20, 12, 20); // 降低初始位置，更接近地面
        
        // 创建渲染器
        this.renderer = new THREE.WebGLRenderer({ antialias: true });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.0;
        
        container.appendChild(this.renderer.domElement);
        
        // 添加轨道控制器 - 配置缩放范围
        this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
        this.controls.enableDamping = true;
        this.controls.dampingFactor = 0.05;
        this.controls.enableZoom = true;
        this.controls.autoRotate = false;
        
        // 设置缩放限制 - 支持从很近到很远的距离
        this.controls.minDistance = 1;     // 最近距离1单位
        this.controls.maxDistance = 10000; // 最远距离10000单位
        this.controls.zoomSpeed = 1.2;     // 缩放速度
        
        // 设置平移和旋转限制
        this.controls.enablePan = true;
        this.controls.panSpeed = 1.0;
        this.controls.rotateSpeed = 1.0;
        
        // 设置垂直旋转角度限制
        this.controls.minPolarAngle = 0;
        this.controls.maxPolarAngle = Math.PI;
        
        // 添加光照
        this.setupLighting();
        
        // 窗口调整大小监听
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 开始渲染循环
        this.animate();
    }
    
    setupLighting() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 主方向光
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 200;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);
        
        // 补充光
        const fillLight = new THREE.DirectionalLight(0x88ccff, 0.3);
        fillLight.position.set(-50, 50, -50);
        this.scene.add(fillLight);
    }
    
    loadModel() {
        const loader = new THREE.GLTFLoader();
        
        // 检查文件路径
        console.log('正在加载模型: gsdx1.glb');
        
        loader.load(
            './gsdx1.glb',
            (gltf) => {
                this.model = gltf.scene;
                
                // 启用阴影
                this.model.traverse((child) => {
                    if (child.isMesh) {
                        child.castShadow = true;
                        child.receiveShadow = true;
                        
                        // 如果材质存在，确保正确渲染
                        if (child.material) {
                            child.material.needsUpdate = true;
                        }
                    }
                });
                
                // 将模型添加到场景
                this.scene.add(this.model);
                
                // 分析并识别道路组件
                this.analyzeRoadComponents();
                
                // 调整相机位置以适应模型
                this.fitCameraToModel();
                
                // 添加地形辅助线（调试用）
                this.addTerrainHelpers();
                
                // 初始化所有道路段为不可见
                this.initializeRoadVisibility();
                
                // 初始化高度显示
                this.updateRoadHeight();
                
                // 隐藏加载界面
                document.getElementById('loading').style.display = 'none';
            },
            (progress) => {
                const percent = (progress.loaded / progress.total * 100).toFixed(2);
                document.getElementById('loading').innerHTML = `
                    <div class="loading-spinner"></div>
                    <div>加载模型中... ${percent}%</div>
                `;
            },
            (error) => {
                console.error('模型加载失败:', error);
                console.error('错误详情:', error.message);
                
                // 提供更详细的错误信息
                let errorMessage = '模型加载失败';
                if (error.message.includes('404')) {
                    errorMessage = '模型文件未找到，请确保 gsdx1.glb 文件在同一目录下';
                } else if (error.message.includes('CORS')) {
                    errorMessage = '跨域错误，请使用 HTTP 服务器访问 (如: python -m http.server 8080)';
                } else if (error.message.includes('network')) {
                    errorMessage = '网络错误，请检查文件路径和服务器状态';
                }
                
                document.getElementById('loading').innerHTML = `
                    <div style="color: #ff4444;">${errorMessage}</div>
                    <div style="font-size: 12px; color: #888; margin-top: 10px;">
                        请确保：<br>
                        1. 使用HTTP服务器访问页面<br>
                        2. gsdx1.glb文件在同一目录<br>
                        3. 浏览器支持WebGL
                    </div>
                `;
            }
        );
    }
    
    analyzeRoadComponents() {
        // 分析模型中的道路组件并创建基于长度的进度段
        const roadMeshes = [];
        const terrainMeshes = [];
        
        // 分离道路和地形组件
        this.model.traverse((child) => {
            if (child.isMesh) {
                const name = child.name.toLowerCase();
                const materialName = child.material && child.material.name ? child.material.name.toLowerCase() : '';
                
                // 检查是否为道路相关组件
                if (name.includes('road') || name.includes('bridge') || name.includes('path') || 
                    materialName.includes('road') || materialName.includes('bridge') || materialName.includes('asphalt')) {
                    roadMeshes.push(child);
                    console.log('发现道路组件:', child.name || 'Unnamed', materialName);
                }
                // 检查是否为地形组件
                else if (name.includes('terrain') || name.includes('ground') || name.includes('surface') ||
                         name.includes('earth') || name.includes('land') || materialName.includes('terrain') ||
                         materialName.includes('ground') || materialName.includes('earth')) {
                    terrainMeshes.push(child);
                    console.log('发现地形组件:', child.name || 'Unnamed', materialName);
                }
                // 如果没有明确标识，根据位置判断（地形通常在底部）
                else {
                    const bbox = new THREE.Box3().setFromObject(child);
                    const center = bbox.getCenter(new THREE.Vector3());
                    
                    // 如果组件主要在较低位置，可能是地形
                    if (bbox.min.y < center.y - (bbox.getSize(new THREE.Vector3()).y * 0.3)) {
                        terrainMeshes.push(child);
                        console.log('推测地形组件 (基于位置):', child.name || 'Unnamed');
                    } else {
                        // 否则可能是道路或其他结构
                        roadMeshes.push(child);
                        console.log('推测道路/结构组件 (基于位置):', child.name || 'Unnamed');
                    }
                }
            }
        });
        
        // 存储地形信息用于后续调整
        this.terrainMeshes = terrainMeshes;
        
        // 如果没有找到任何道路组件，使用所有非地形网格
        if (roadMeshes.length === 0) {
            this.model.traverse((child) => {
                if (child.isMesh && !terrainMeshes.includes(child)) {
                    roadMeshes.push(child);
                }
            });
        }
        
        // 调整道路位置到地形表面
        this.adjustRoadToTerrain(roadMeshes);
        
        // 分析道路长度和创建分段
        this.createRoadSegmentsByLength(roadMeshes);
        
        console.log(`识别到 ${this.roadSegments.length} 个道路段`);
        console.log(`识别到 ${terrainMeshes.length} 个地形组件`);
    }
    
    createRoadSegmentsByLength(roadMeshes) {
        // 计算道路的总体边界盒和方向
        const totalBoundingBox = new THREE.Box3();
        roadMeshes.forEach(mesh => {
            const box = new THREE.Box3().setFromObject(mesh);
            totalBoundingBox.union(box);
        });

        const size = totalBoundingBox.getSize(new THREE.Vector3());
        // 假设道路沿着最长的轴延伸
        const roadLength = Math.max(size.x, size.z);

        // 将道路分成更多段落以实现更平滑的生长动画
        const segmentCount = this.growthAnimationEnabled ? 16 : 8; // 启用生长动画时使用更多段落
        const segmentLength = roadLength / segmentCount;
        
        // 确定道路的主要方向
        const roadDirection = size.x > size.z ? 'x' : 'z';
        const minPos = roadDirection === 'x' ? totalBoundingBox.min.x : totalBoundingBox.min.z;
        
        for (let i = 0; i < segmentCount; i++) {
            const segmentStart = minPos + (i * segmentLength);
            const segmentEnd = minPos + ((i + 1) * segmentLength);
            
            // 找到属于这个段落的网格
            const segmentMeshes = roadMeshes.filter(mesh => {
                const box = new THREE.Box3().setFromObject(mesh);
                const center = box.getCenter(new THREE.Vector3());
                const pos = roadDirection === 'x' ? center.x : center.z;
                return pos >= segmentStart && pos < segmentEnd;
            });
            
            // 如果没有网格完全在这个段落内，分配最近的网格
            if (segmentMeshes.length === 0 && roadMeshes.length > 0) {
                const segmentCenter = segmentStart + (segmentLength / 2);
                let closestMesh = roadMeshes[0];
                let closestDistance = Infinity;
                
                roadMeshes.forEach(mesh => {
                    const box = new THREE.Box3().setFromObject(mesh);
                    const center = box.getCenter(new THREE.Vector3());
                    const pos = roadDirection === 'x' ? center.x : center.z;
                    const distance = Math.abs(pos - segmentCenter);
                    
                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestMesh = mesh;
                    }
                });
                
                segmentMeshes.push(closestMesh);
            }
            
            // 创建道路段
            const segment = {
                id: i,
                name: `道路段 ${i + 1}`,
                meshes: segmentMeshes,
                originalMaterials: segmentMeshes.map(mesh => mesh.material ? mesh.material.clone() : null),
                progressMaterials: segmentMeshes.map(mesh => mesh.material ? mesh.material.clone() : null),
                startPosition: segmentStart,
                endPosition: segmentEnd,
                length: segmentLength,
                lengthProgress: (i + 1) / segmentCount, // 这个段落在总长度中的位置 (0-1)
                startMonth: i * (this.projectDuration / segmentCount), // 按顺序开始施工
                duration: this.projectDuration / segmentCount + (Math.random() - 0.5) * 2, // 稍微随机化持续时间
                completed: false,
                visible: false, // 初始状态不可见
                centerPoint: new THREE.Vector3(
                    (segmentStart + segmentEnd) / 2,
                    0, // Y坐标稍后会根据地形调整
                    roadDirection === 'x' ? totalBoundingBox.getCenter(new THREE.Vector3()).z : totalBoundingBox.getCenter(new THREE.Vector3()).x
                ),
                // 生长动画相关属性
                growthProgress: 0, // 生长进度 (0-1)
                originalGeometries: [], // 原始几何体
                clippedMeshes: [], // 裁剪后的网格
                constructionFront: null, // 施工前沿位置
                particleSystem: null, // 粒子系统
                equipment: null // 施工设备
            };

            // 如果启用生长动画，准备几何体数据
            if (this.growthAnimationEnabled) {
                this.prepareSegmentForGrowth(segment, roadDirection);
            }

            this.roadSegments.push(segment);
        }
    }
    
    createGenericRoadSegments() {
        // 为整个模型创建虚拟的道路段
        const meshes = [];
        this.model.traverse((child) => {
            if (child.isMesh) {
                meshes.push(child);
            }
        });
        
        // 将网格分组为道路段
        const segmentCount = Math.min(8, Math.max(3, meshes.length));
        const meshesPerSegment = Math.ceil(meshes.length / segmentCount);
        
        for (let i = 0; i < segmentCount; i++) {
            const startIndex = i * meshesPerSegment;
            const endIndex = Math.min(startIndex + meshesPerSegment, meshes.length);
            const segmentMeshes = meshes.slice(startIndex, endIndex);
            
            const segment = {
                meshes: segmentMeshes,
                originalMaterials: segmentMeshes.map(mesh => mesh.material ? mesh.material.clone() : null),
                startMonth: i * 2, // 错开开始时间
                duration: Math.floor(Math.random() * 4) + 3, // 3-6个月工期
                completed: false,
                name: `道路段 ${i + 1}`
            };
            
            this.roadSegments.push(segment);
        }
    }
    
    fitCameraToModel() {
        const box = new THREE.Box3().setFromObject(this.model);
        const size = box.getSize(new THREE.Vector3());
        const center = box.getCenter(new THREE.Vector3());
        
        const maxSize = Math.max(size.x, size.y, size.z);
        const fitHeightDistance = maxSize / (2 * Math.atan(Math.PI * this.camera.fov / 360));
        const fitWidthDistance = fitHeightDistance / this.camera.aspect;
        const distance = 0.6 * Math.max(fitHeightDistance, fitWidthDistance); // 减少距离倍数，更靠近模型
        
        const direction = this.controls.object.position.clone()
            .sub(this.controls.target)
            .normalize()
            .multiplyScalar(distance);
        
        this.controls.target.copy(center);
        this.camera.position.copy(center).add(direction);
        
        this.camera.near = Math.max(0.1, distance / 1000);
        this.camera.far = Math.max(10000, distance * 10);
        this.camera.updateProjectionMatrix();
        
        // 保存原始摄像机位置，用于跟随模式切换
        this.originalCameraPosition = this.camera.position.clone();
        this.originalCameraTarget = this.controls.target.clone();
        
        this.controls.update();
    }
    
    adjustRoadToTerrain(roadMeshes) {
        if (!this.terrainMeshes || this.terrainMeshes.length === 0) {
            console.log('没有找到地形组件，跳过道路位置调整');
            return;
        }
        
        // 计算地形的平均高度和范围
        let terrainMinY = Infinity;
        let terrainMaxY = -Infinity;
        let terrainAvgY = 0;
        let sampleCount = 0;
        
        this.terrainMeshes.forEach(terrainMesh => {
            const terrainBBox = new THREE.Box3().setFromObject(terrainMesh);
            terrainMinY = Math.min(terrainMinY, terrainBBox.min.y);
            terrainMaxY = Math.max(terrainMaxY, terrainBBox.max.y);
            
            // 采样地形表面高度点
            const terrainCenter = terrainBBox.getCenter(new THREE.Vector3());
            terrainAvgY += terrainCenter.y;
            sampleCount++;
        });
        
        if (sampleCount > 0) {
            terrainAvgY /= sampleCount;
        }
        
        console.log(`地形高度范围: ${terrainMinY.toFixed(2)} 到 ${terrainMaxY.toFixed(2)}, 平均: ${terrainAvgY.toFixed(2)}`);
        
        // 调整每个道路组件的位置
        roadMeshes.forEach(roadMesh => {
            const roadBBox = new THREE.Box3().setFromObject(roadMesh);
            const roadCenter = roadBBox.getCenter(new THREE.Vector3());
            
            // 计算道路在这个位置的理想高度
            const targetHeight = this.getTerrainHeightAt(roadCenter.x, roadCenter.z);
            const currentBottom = roadBBox.min.y;
            
            // 计算需要调整的高度（将道路底部对齐到地形表面）
            // 使用更大的偏移量确保道路在地形表面上方
            const heightAdjustment = targetHeight - currentBottom + 2.0; // 增加到2.0单位的偏移
            
            // 调整道路位置
            roadMesh.position.y += heightAdjustment;
            roadMesh.updateMatrixWorld(true);
            
            console.log(`调整道路 ${roadMesh.name || 'Unnamed'}: 高度偏移 ${heightAdjustment.toFixed(2)}`);
        });
    }
    
    getTerrainHeightAt(x, z) {
        if (!this.terrainMeshes || this.terrainMeshes.length === 0) {
            return 0;
        }
        
        // 使用射线投射来获取地形在指定xy位置的精确高度
        const raycaster = new THREE.Raycaster();
        const rayOrigin = new THREE.Vector3(x, 1000, z); // 从很高的位置向下投射
        const rayDirection = new THREE.Vector3(0, -1, 0); // 向下
        
        raycaster.set(rayOrigin, rayDirection);
        
        // 对所有地形网格进行射线检测
        const intersections = raycaster.intersectObjects(this.terrainMeshes, true);
        
        if (intersections.length > 0) {
            // 返回最高的交点（最接近射线起点的）
            return intersections[0].point.y;
        }
        
        // 如果射线检测失败，返回地形的平均高度
        let avgHeight = 0;
        let count = 0;
        
        this.terrainMeshes.forEach(terrainMesh => {
            const bbox = new THREE.Box3().setFromObject(terrainMesh);
            avgHeight += bbox.max.y; // 使用地形顶部作为参考
            count++;
        });
        
        // 如果射线检测失败，返回地形的平均高度并加上安全偏移
        const safeHeight = count > 0 ? avgHeight / count + 3.0 : 3.0;
        console.log(`射线检测失败，使用安全高度: ${safeHeight}`);
        return safeHeight;
    }
    
    addTerrainHelpers() {
        if (!this.terrainMeshes || this.terrainMeshes.length === 0) {
            return;
        }
        
        // 创建地形边界框辅助线（可选，用于调试）
        this.terrainMeshes.forEach((terrainMesh, index) => {
            const bbox = new THREE.Box3().setFromObject(terrainMesh);
            const helper = new THREE.Box3Helper(bbox, 0x00ff00);
            helper.name = `TerrainHelper_${index}`;
            helper.visible = false; // 默认隐藏，需要时可在控制台显示
            this.scene.add(helper);
        });
        
        console.log('地形辅助线已添加（默认隐藏）');
        console.log('要显示地形边界，请在控制台运行: scene.children.forEach(c => { if(c.name.includes("TerrainHelper")) c.visible = true; })');
    }
    
    // 手动微调道路高度的方法（如果自动调整不够准确）
    adjustRoadHeight(offset = 0) {
        this.roadSegments.forEach(segment => {
            if (segment.meshes) {
                segment.meshes.forEach(mesh => {
                    if (mesh) {
                        mesh.position.y += offset;
                        mesh.updateMatrixWorld(true);
                    }
                });
            } else if (segment.mesh) {
                segment.mesh.position.y += offset;
                segment.mesh.updateMatrixWorld(true);
            }
        });
        console.log(`所有道路段已调整高度: ${offset}`);
    }
    
    // 强制将所有道路段设置到指定高度
    setRoadAbsoluteHeight(height = 5) {
        this.roadSegments.forEach((segment, index) => {
            if (segment.meshes) {
                segment.meshes.forEach(mesh => {
                    if (mesh) {
                        mesh.position.y = height;
                        mesh.updateMatrixWorld(true);
                    }
                });
            } else if (segment.mesh) {
                segment.mesh.position.y = height;
                segment.mesh.updateMatrixWorld(true);
            }
        });
        console.log(`所有道路段已设置到绝对高度: ${height}`);
    }
    
    // 更新道路高度（基于偏移量）
    updateRoadHeight() {
        // 更新高度显示
        const heightValue = document.getElementById('height-value');
        if (heightValue) {
            heightValue.textContent = this.roadHeightOffset.toFixed(1);
        }
        
        // 应用高度偏移到所有道路段
        this.roadSegments.forEach(segment => {
            if (segment.meshes) {
                segment.meshes.forEach(mesh => {
                    if (mesh && mesh.userData.baseHeight !== undefined) {
                        // 如果有基础高度记录，在此基础上应用偏移
                        mesh.position.y = mesh.userData.baseHeight + this.roadHeightOffset;
                        mesh.updateMatrixWorld(true);
                    } else if (mesh) {
                        // 第一次调整，记录当前位置为基础高度
                        if (mesh.userData.baseHeight === undefined) {
                            mesh.userData.baseHeight = mesh.position.y;
                        }
                        mesh.position.y = mesh.userData.baseHeight + this.roadHeightOffset;
                        mesh.updateMatrixWorld(true);
                    }
                });
            } else if (segment.mesh) {
                if (segment.mesh.userData.baseHeight !== undefined) {
                    segment.mesh.position.y = segment.mesh.userData.baseHeight + this.roadHeightOffset;
                    segment.mesh.updateMatrixWorld(true);
                } else {
                    if (segment.mesh.userData.baseHeight === undefined) {
                        segment.mesh.userData.baseHeight = segment.mesh.position.y;
                    }
                    segment.mesh.position.y = segment.mesh.userData.baseHeight + this.roadHeightOffset;
                    segment.mesh.updateMatrixWorld(true);
                }
            }
        });
        
        console.log(`道路高度已调整到偏移: ${this.roadHeightOffset}`);
    }
    
    // 调试：显示道路段的当前位置信息
    showRoadPositions() {
        console.log('=== 道路段位置信息 ===');
        this.roadSegments.forEach((segment, index) => {
            if (segment.meshes && segment.meshes.length > 0) {
                const firstMesh = segment.meshes[0];
                if (firstMesh) {
                    const bbox = new THREE.Box3().setFromObject(firstMesh);
                    const center = bbox.getCenter(new THREE.Vector3());
                    console.log(`段落 ${index + 1}: 中心=${center.x.toFixed(2)}, ${center.y.toFixed(2)}, ${center.z.toFixed(2)}, 底部=${bbox.min.y.toFixed(2)}, 顶部=${bbox.max.y.toFixed(2)}`);
                }
            }
        });
    }
    
    initializeRoadVisibility() {
        // 初始化时隐藏所有道路段，根据进度显示
        this.roadSegments.forEach(segment => {
            this.setSegmentVisibility(segment, false);
        });
    }
    
    setupEventListeners() {
        const timelineSlider = document.getElementById('timeline-slider');
        const playBtn = document.getElementById('play-btn');
        const pauseBtn = document.getElementById('pause-btn');
        const resetBtn = document.getElementById('reset-btn');
        const followBtn = document.getElementById('follow-btn');
        
        // 道路高度控制元素
        const heightSlider = document.getElementById('height-slider');
        const heightValue = document.getElementById('height-value');
        const heightUp = document.getElementById('height-up');
        const heightDown = document.getElementById('height-down');
        const heightReset = document.getElementById('height-reset');
        
        timelineSlider.addEventListener('input', (e) => {
            this.currentProgress = parseInt(e.target.value);
            this.updateProgress();
        });
        
        playBtn.addEventListener('click', () => this.startAnimation());
        pauseBtn.addEventListener('click', () => this.pauseAnimation());
        resetBtn.addEventListener('click', () => this.resetProgress());
        
        // 跟随模式按钮（如果存在）
        if (followBtn) {
            followBtn.addEventListener('click', () => this.toggleFollowMode());
        }
        
        // 道路高度调节事件监听
        if (heightSlider) {
            heightSlider.addEventListener('input', (e) => {
                this.roadHeightOffset = parseFloat(e.target.value);
                this.updateRoadHeight();
            });
        }
        
        if (heightUp) {
            heightUp.addEventListener('click', () => {
                this.roadHeightOffset += 1;
                this.roadHeightOffset = Math.min(20, this.roadHeightOffset); // 限制最大值
                if (heightSlider) heightSlider.value = this.roadHeightOffset;
                this.updateRoadHeight();
            });
        }
        
        if (heightDown) {
            heightDown.addEventListener('click', () => {
                this.roadHeightOffset -= 1;
                this.roadHeightOffset = Math.max(-10, this.roadHeightOffset); // 限制最小值
                if (heightSlider) heightSlider.value = this.roadHeightOffset;
                this.updateRoadHeight();
            });
        }
        
        if (heightReset) {
            heightReset.addEventListener('click', () => {
                this.roadHeightOffset = 0;
                if (heightSlider) heightSlider.value = 0;
                this.updateRoadHeight();
            });
        }
    }
    
    setupProgressSimulation() {
        // 为每个道路段设置进度状态
        this.roadSegments.forEach((segment, index) => {
            segment.id = index;
            segment.progress = 0; // 0-100
        });
    }
    
    updateProgress() {
        const currentMonth = (this.currentProgress / 100) * this.projectDuration;
        
        // 更新日期显示
        const currentDate = new Date(this.startDate);
        currentDate.setMonth(currentDate.getMonth() + Math.floor(currentMonth));
        document.getElementById('current-date').textContent = 
            `${currentDate.getFullYear()}年${currentDate.getMonth() + 1}月`;
        
        // 计算基于道路长度的实际进度
        const roadProgressPercentage = this.calculateRoadProgress(currentMonth);
        document.getElementById('progress-percentage').textContent = 
            `${Math.round(roadProgressPercentage)}%`;
        
        // 更新滑块位置
        document.getElementById('timeline-slider').value = this.currentProgress;
        
        // 更新每个道路段的进度和可视化
        this.updateRoadSegments(currentMonth);
        
        // 如果启用跟随模式，更新摄像机位置
        if (this.followMode && this.isPlaying) {
            this.updateCameraFollow(currentMonth);
        }
    }
    
    calculateRoadProgress(currentMonth) {
        let completedLength = 0;
        let totalLength = 0;
        
        this.roadSegments.forEach(segment => {
            totalLength += segment.length;
            const segmentEndMonth = segment.startMonth + segment.duration;
            
            if (currentMonth >= segmentEndMonth) {
                // 完全完成
                completedLength += segment.length;
            } else if (currentMonth > segment.startMonth) {
                // 部分完成
                const segmentProgress = (currentMonth - segment.startMonth) / segment.duration;
                completedLength += segment.length * segmentProgress;
            }
        });
        
        return totalLength > 0 ? (completedLength / totalLength) * 100 : 0;
    }
    
    updateRoadSegments(currentMonth) {
        this.roadSegments.forEach(segment => {
            const segmentEndMonth = segment.startMonth + segment.duration;
            
            if (currentMonth < segment.startMonth) {
                // 未开始 - 完全隐藏道路段
                segment.progress = 0;
                segment.visible = false;
                this.setSegmentVisibility(segment, false);
                this.setSegmentMaterial(segment, 'not-started');
            } else if (currentMonth >= segmentEndMonth) {
                // 已完成 - 显示完整道路段
                segment.progress = 100;
                segment.visible = true;
                this.setSegmentVisibility(segment, true);
                this.setSegmentMaterial(segment, 'completed');
            } else {
                // 进行中 - 根据进度逐步显示道路段
                segment.progress = ((currentMonth - segment.startMonth) / segment.duration) * 100;
                segment.visible = true;
                
                // 渐进式加载：根据进度控制透明度和可见性
                this.setProgressiveVisibility(segment, segment.progress);
                this.setSegmentMaterial(segment, 'in-progress');
            }
        });
    }
    
    updateCameraFollow(currentMonth) {
        // 找到当前正在建设的道路段的前沿位置
        const frontPosition = this.getRoadConstructionFront(currentMonth);
        if (!frontPosition) return;
        
        // 计算摄像机的目标位置和朝向
        const cameraTarget = frontPosition.clone();
        
        // 稍微向前看一点，让视角更有前瞻性
        const roadDirection = this.getRoadDirection();
        const lookAheadDistance = 3; // 向前看的距离
        if (roadDirection === 'x') {
            cameraTarget.x += lookAheadDistance;
        } else {
            cameraTarget.z += lookAheadDistance;
        }
        
        // 根据道路方向计算摄像机位置
        const cameraPosition = this.calculateCameraPosition(frontPosition, roadDirection);
        
        // 平滑移动摄像机
        this.smoothMoveCameraTo(cameraPosition, cameraTarget, 600); // 更快的响应速度
    }
    
    getRoadConstructionFront(currentMonth) {
        // 找到当前最前沿的建设位置
        let frontmostSegment = null;
        let maxProgress = -1;
        
        for (const segment of this.roadSegments) {
            const segmentEndMonth = segment.startMonth + segment.duration;
            
            if (currentMonth > segment.startMonth && currentMonth <= segmentEndMonth) {
                // 正在建设中的段落
                if (segment.lengthProgress > maxProgress) {
                    maxProgress = segment.lengthProgress;
                    frontmostSegment = segment;
                }
            } else if (currentMonth > segmentEndMonth) {
                // 已完成的段落，也要考虑
                if (segment.lengthProgress > maxProgress) {
                    maxProgress = segment.lengthProgress;
                    frontmostSegment = segment;
                }
            }
        }
        
        if (!frontmostSegment) {
            // 如果还没有开始建设，返回第一个段落的起点
            if (this.roadSegments.length > 0) {
                const firstSegment = this.roadSegments[0];
                return this.getSegmentCenterPoint(firstSegment);
            }
            return null;
        }
        
        // 计算段落内的具体进度位置
        const segmentProgress = ((currentMonth - frontmostSegment.startMonth) / frontmostSegment.duration);
        const clampedProgress = Math.max(0, Math.min(1, segmentProgress));
        
        // 在段落内部插值计算精确位置
        return this.getSegmentProgressPosition(frontmostSegment, clampedProgress);
    }
    
    getSegmentCenterPoint(segment) {
        // 更新段落中心点的Y坐标（考虑地形高度）
        if (segment.centerPoint) {
            const terrainHeight = this.getTerrainHeightAt(segment.centerPoint.x, segment.centerPoint.z);
            segment.centerPoint.y = terrainHeight + 1.5; // 确保在地形上方
            return segment.centerPoint.clone();
        }
        
        // 如果没有预设中心点，从网格计算
        if (segment.meshes && segment.meshes.length > 0) {
            const bbox = new THREE.Box3();
            segment.meshes.forEach(mesh => {
                if (mesh) {
                    bbox.expandByObject(mesh);
                }
            });
            return bbox.getCenter(new THREE.Vector3());
        }
        
        return new THREE.Vector3(0, 0, 0);
    }
    
    getSegmentProgressPosition(segment, progress) {
        const centerPoint = this.getSegmentCenterPoint(segment);
        
        // 根据道路方向和进度计算具体位置
        const roadDirection = this.getRoadDirection();
        const segmentLength = segment.length;
        const progressOffset = (progress - 0.5) * segmentLength;
        
        if (roadDirection === 'x') {
            centerPoint.x += progressOffset;
        } else {
            centerPoint.z += progressOffset;
        }
        
        // 更新Y坐标以匹配地形，并添加额外偏移确保在地面上方
        centerPoint.y = this.getTerrainHeightAt(centerPoint.x, centerPoint.z) + 1.5;
        
        return centerPoint;
    }
    
    getRoadDirection() {
        // 分析道路的主要方向
        if (this.roadSegments.length === 0) return 'x';
        
        const firstSegment = this.roadSegments[0];
        const lastSegment = this.roadSegments[this.roadSegments.length - 1];
        
        const firstCenter = this.getSegmentCenterPoint(firstSegment);
        const lastCenter = this.getSegmentCenterPoint(lastSegment);
        
        const deltaX = Math.abs(lastCenter.x - firstCenter.x);
        const deltaZ = Math.abs(lastCenter.z - firstCenter.z);
        
        return deltaX > deltaZ ? 'x' : 'z';
    }
    
    calculateCameraPosition(targetPoint, roadDirection) {
        const position = targetPoint.clone();
        
        // 获取地形高度作为基准
        const groundHeight = this.getTerrainHeightAt(targetPoint.x, targetPoint.z);
        
        // 根据道路方向设置摄像机偏移
        if (roadDirection === 'x') {
            // 道路沿X轴，摄像机在Z轴和X轴方向偏移
            position.z += this.cameraFollowDistance * this.cameraSideOffset; // 侧向偏移
            position.x -= this.cameraFollowDistance * this.cameraBackwardOffset; // 后退偏移
        } else {
            // 道路沿Z轴，摄像机在X轴和Z轴方向偏移
            position.x += this.cameraFollowDistance * this.cameraSideOffset; // 侧向偏移
            position.z -= this.cameraFollowDistance * this.cameraBackwardOffset; // 后退偏移
        }
        
        // 设置相对地形的高度
        position.y = groundHeight + this.cameraFollowHeight;
        
        return position;
    }
    
    smoothMoveCameraTo(targetPosition, targetLookAt, duration = 1000) {
        if (!this.camera || !this.controls) return;
        
        // 保存当前位置
        const startPosition = this.camera.position.clone();
        const startTarget = this.controls.target.clone();
        
        // 创建平滑过渡动画
        const startTime = Date.now();
        
        const animateCamera = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const eased = this.easeInOutCubic(progress);
            
            // 插值计算摄像机位置
            this.camera.position.lerpVectors(startPosition, targetPosition, eased);
            this.controls.target.lerpVectors(startTarget, targetLookAt, eased);
            
            this.controls.update();
            
            if (progress < 1) {
                requestAnimationFrame(animateCamera);
            }
        };
        
        animateCamera();
    }
    
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }
    
    setSegmentVisibility(segment, visible) {
        if (segment.meshes) {
            segment.meshes.forEach(mesh => {
                if (mesh) {
                    mesh.visible = visible;
                }
            });
        } else if (segment.mesh) {
            segment.mesh.visible = visible;
        }
    }
    
    setProgressiveVisibility(segment, progressPercent) {
        // 渐进式显示：根据施工进度逐步显示道路段
        const showThreshold = 10; // 开始施工10%后道路开始显现
        const fullVisibleThreshold = 30; // 施工30%后完全可见
        
        if (progressPercent < showThreshold) {
            // 施工初期 - 道路尚未显现
            this.setSegmentVisibility(segment, false);
        } else if (progressPercent >= fullVisibleThreshold) {
            // 施工中后期 - 道路完全可见
            this.setSegmentVisibility(segment, true);
            this.setSegmentOpacity(segment, 1.0);
        } else {
            // 施工前期 - 道路逐渐显现
            this.setSegmentVisibility(segment, true);
            
            // 计算透明度：从10%到30%进度时，透明度从0.3到1.0
            const visibilityProgress = (progressPercent - showThreshold) / (fullVisibleThreshold - showThreshold);
            const opacity = 0.3 + (0.7 * visibilityProgress);
            this.setSegmentOpacity(segment, opacity);
        }
    }
    
    setSegmentOpacity(segment, opacity) {
        if (segment.meshes) {
            segment.meshes.forEach((mesh, index) => {
                if (mesh && mesh.material) {
                    // 创建新材质或修改现有材质
                    if (!mesh.material.transparent || mesh.material.opacity !== opacity) {
                        // 克隆材质以避免影响其他使用相同材质的对象
                        const material = mesh.material.clone();
                        material.transparent = true;
                        material.opacity = opacity;
                        material.needsUpdate = true;
                        mesh.material = material;
                    }
                }
            });
        } else if (segment.mesh && segment.mesh.material) {
            const material = segment.mesh.material.clone();
            material.transparent = true;
            material.opacity = opacity;
            material.needsUpdate = true;
            segment.mesh.material = material;
        }
    }
    
    setSegmentMaterial(segment, status) {
        const colors = {
            'not-started': 0x666666,    // 灰色
            'in-progress': 0xffaa00,    // 橙色
            'completed': 0x00ff88       // 绿色
        };
        
        const color = colors[status];
        const emissiveIntensity = {
            'not-started': 0.05,
            'in-progress': 0.3,    // 施工中更亮
            'completed': 0.15      // 已完成适中亮度
        };
        
        if (segment.meshes) {
            // 基于长度的分段
            segment.meshes.forEach((mesh, index) => {
                if (mesh && mesh.material && segment.originalMaterials[index]) {
                    // 检查是否已经有修改过的材质，如果有则基于它进行修改
                    let baseMaterial = mesh.material.isCloned ? mesh.material : segment.originalMaterials[index];
                    const material = baseMaterial.clone();
                    material.isCloned = true; // 标记为已克隆
                    
                    // 设置颜色
                    if (material.color) {
                        material.color.setHex(color);
                    }
                    
                    // 设置发光效果
                    if (material.emissive !== undefined) {
                        material.emissive.setHex(color);
                        material.emissiveIntensity = emissiveIntensity[status];
                    }
                    
                    // 对于施工中状态，添加特殊效果
                    if (status === 'in-progress') {
                        // 注意：不要在这里设置透明度，因为渐进式加载会控制透明度
                        material.transparent = true;
                        
                        // 添加轻微的闪烁效果
                        const time = Date.now() * 0.001;
                        const pulse = Math.sin(time * 3) * 0.1 + 0.9;
                        material.emissiveIntensity = emissiveIntensity[status] * pulse;
                    } else {
                        // 完成状态设为不透明，未开始状态保持隐藏
                        if (status === 'completed') {
                            material.transparent = false;
                            material.opacity = 1.0;
                        }
                    }
                    
                    material.needsUpdate = true;
                    mesh.material = material;
                }
            });
        } else if (segment.mesh) {
            // 特定道路分段（向后兼容）
            if (segment.mesh.material && segment.originalMaterial) {
                const material = segment.originalMaterial.clone();
                material.color.setHex(color);
                material.emissive.setHex(color);
                material.emissiveIntensity = emissiveIntensity[status];
                
                if (status === 'in-progress') {
                    material.transparent = true;
                } else if (status === 'completed') {
                    material.transparent = false;
                    material.opacity = 1.0;
                }
                
                material.needsUpdate = true;
                segment.mesh.material = material;
            }
        }
    }
    
    startAnimation() {
        if (this.isPlaying) return;
        
        this.isPlaying = true;
        this.playInterval = setInterval(() => {
            this.currentProgress += 0.5; // 每100ms增加0.5%
            if (this.currentProgress >= 100) {
                this.currentProgress = 100;
                this.pauseAnimation();
            }
            this.updateProgress();
        }, 100);
    }
    
    pauseAnimation() {
        this.isPlaying = false;
        if (this.playInterval) {
            clearInterval(this.playInterval);
            this.playInterval = null;
        }
    }
    
    resetProgress() {
        this.pauseAnimation();
        this.currentProgress = 0;
        this.updateProgress();
        
        // 重置摄像机到原始位置
        if (this.originalCameraPosition && this.originalCameraTarget) {
            this.camera.position.copy(this.originalCameraPosition);
            this.controls.target.copy(this.originalCameraTarget);
            this.controls.update();
        }
    }
    
    toggleFollowMode() {
        this.followMode = !this.followMode;
        
        const followBtn = document.getElementById('follow-btn');
        if (followBtn) {
            followBtn.textContent = this.followMode ? '取消跟随' : '摄像机跟随';
            followBtn.className = this.followMode ? 'btn btn-primary' : 'btn btn-secondary';
        }
        
        // 如果关闭跟随模式，恢复到原始位置
        if (!this.followMode && this.originalCameraPosition && this.originalCameraTarget) {
            this.smoothMoveCameraTo(this.originalCameraPosition, this.originalCameraTarget, 2000);
        }
        
        console.log('摄像机跟随模式:', this.followMode ? '开启' : '关闭');
    }
    
    // 调整摄像机跟随参数的方法
    setCameraFollowParams(height = 6, distance = 12, backwardOffset = 0.8, sideOffset = 0.6) {
        this.cameraFollowHeight = height;
        this.cameraFollowDistance = distance;
        this.cameraBackwardOffset = backwardOffset;
        this.cameraSideOffset = sideOffset;
        console.log(`摄像机跟随参数已更新: 高度=${height}, 距离=${distance}, 后退偏移=${backwardOffset}, 侧向偏移=${sideOffset}`);
    }
    
    // 快速调整摄像机到不同预设位置
    setCameraPreset(preset) {
        const presets = {
            'close': { height: 4, distance: 8, backward: 0.6, side: 0.4 },      // 近距离观察
            'medium': { height: 6, distance: 12, backward: 0.8, side: 0.6 },    // 中等距离（默认）
            'far': { height: 10, distance: 18, backward: 1.0, side: 0.8 },      // 远距离观察
            'aerial': { height: 15, distance: 25, backward: 1.2, side: 1.0 }    // 航拍视角
        };
        
        if (presets[preset]) {
            const p = presets[preset];
            this.setCameraFollowParams(p.height, p.distance, p.backward, p.side);
        } else {
            console.log('可用预设: close, medium, far, aerial');
        }
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.controls) {
            this.controls.update();
        }
        
        // 更新施工中道路段的闪烁效果
        this.updateInProgressEffects();
        
        if (this.renderer && this.scene && this.camera) {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    updateInProgressEffects() {
        const time = Date.now() * 0.001;
        
        this.roadSegments.forEach(segment => {
            if (segment.visible && segment.progress > 0 && segment.progress < 100) {
                // 为正在施工的道路段添加动态效果
                if (segment.meshes) {
                    segment.meshes.forEach(mesh => {
                        if (mesh && mesh.material && mesh.material.emissive) {
                            const pulse = Math.sin(time * 2) * 0.1 + 0.9;
                            mesh.material.emissiveIntensity = 0.3 * pulse;
                            mesh.material.needsUpdate = true;
                        }
                    });
                }
            }
        });
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.roadManager = new RoadProgressManager();
    
    // 在控制台提供便捷访问
    console.log('路桥工程进度管理系统已启动 - 支持渐进式道路加载');
    console.log('摄像机控制:');
    console.log('roadManager.setCameraPreset("close")  - 近距离视角');
    console.log('roadManager.setCameraPreset("medium") - 中等距离视角(默认)');
    console.log('roadManager.setCameraPreset("far")    - 远距离视角');
    console.log('roadManager.setCameraPreset("aerial") - 航拍视角');
    console.log('');
    console.log('道路高度调试:');
    console.log('roadManager.showRoadPositions()       - 显示道路位置信息');
    console.log('roadManager.adjustRoadHeight(2)       - 调整道路高度(+2单位)');
    console.log('roadManager.setRoadAbsoluteHeight(5)  - 设置道路绝对高度(5单位)');
    console.log('roadManager.roadHeightOffset          - 当前高度偏移量');
    console.log('');
    console.log('渐进式加载说明:');
    console.log('- 施工进度 0-10%: 道路段完全隐藏');
    console.log('- 施工进度 10-30%: 道路段逐渐显现(透明度0.3-1.0)');
    console.log('- 施工进度 30%+: 道路段完全可见，使用施工中材质');
    console.log('- 施工完成: 道路段使用完工材质');
    console.log('');
    console.log('提示: 使用界面上的道路高度滑块和按钮进行实时调节');
});