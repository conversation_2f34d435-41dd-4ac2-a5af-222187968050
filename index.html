<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路桥工程进度管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #canvas-container {
            width: 100%;
            height: 100%;
        }
        
        #ui-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            padding: 20px;
            min-width: 300px;
            z-index: 100;
        }
        
        .panel-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #00ff88;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #cccccc;
        }
        
        .timeline-container {
            margin-bottom: 15px;
        }
        
        #timeline-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #333;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
            cursor: pointer;
        }
        
        #timeline-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #00ff88;
            cursor: pointer;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }
        
        #timeline-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #00ff88;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .current-date {
            font-size: 16px;
            color: #00ff88;
            font-weight: bold;
        }
        
        .progress-percentage {
            font-size: 14px;
            color: #ffffff;
        }
        
        .controls {
            display: flex;
            gap: 8px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
            min-width: 70px;
            text-align: center;
        }
        
        .btn-primary {
            background: #00ff88;
            color: #000000;
        }
        
        .btn-primary:hover {
            background: #00cc6a;
        }
        
        .btn-secondary {
            background: #333;
            color: #ffffff;
        }
        
        .btn-secondary:hover {
            background: #555;
        }
        
        .progress-legend {
            border-top: 1px solid #333;
            padding-top: 15px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .legend-color {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 10px;
        }
        
        .legend-text {
            font-size: 12px;
            color: #cccccc;
        }
        
        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #00ff88;
            font-size: 18px;
            z-index: 200;
        }
        
        .loading-spinner {
            border: 3px solid #333;
            border-top: 3px solid #00ff88;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 道路高度控制样式 */
        .height-control {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .height-display {
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: #00ff88;
            margin-bottom: 5px;
        }
        
        .height-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #333;
            outline: none;
            -webkit-appearance: none;
            appearance: none;
            cursor: pointer;
        }
        
        .height-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff6600;
            cursor: pointer;
            box-shadow: 0 0 8px rgba(255, 102, 0, 0.6);
        }
        
        .height-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff6600;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 8px rgba(255, 102, 0, 0.6);
        }
        
        .height-buttons {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 11px;
            min-width: 50px;
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="loading">
            <div class="loading-spinner"></div>
            <div>加载模型中...</div>
        </div>
        
        <div id="canvas-container"></div>
        
        <div id="ui-panel">
            <div class="panel-title">路桥工程进度管理</div>
            
            <div class="control-group">
                <div class="progress-info">
                    <div class="current-date" id="current-date">2024年1月</div>
                    <div class="progress-percentage" id="progress-percentage">0%</div>
                </div>
                
                <div class="timeline-container">
                    <label class="control-label">工程时间轴</label>
                    <input type="range" id="timeline-slider" min="0" max="100" value="0" step="1">
                </div>
                
                <div class="controls">
                    <button class="btn btn-primary" id="play-btn">播放</button>
                    <button class="btn btn-secondary" id="pause-btn">暂停</button>
                    <button class="btn btn-secondary" id="reset-btn">重置</button>
                    <button class="btn btn-primary" id="follow-btn">摄像机跟随</button>
                </div>
            </div>
            
            <div class="control-group">
                <label class="control-label">道路高度调节</label>
                <div class="height-control">
                    <div class="height-display">
                        <span id="height-value">0</span> 单位
                    </div>
                    <input type="range" id="height-slider" min="-500" max="500" value="0" step="50" class="height-slider">
                    <div class="height-buttons">
                        <button class="btn btn-secondary btn-small" id="height-down">-50</button>
                        <button class="btn btn-secondary btn-small" id="height-reset">重置</button>
                        <button class="btn btn-secondary btn-small" id="height-up">+50</button>
                    </div>
                </div>
            </div>
            
            <div class="progress-legend">
                <div class="control-label">进度图例</div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #666;"></div>
                    <span class="legend-text">未开工</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #ffaa00;"></div>
                    <span class="legend-text">施工中</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #00ff88;"></div>
                    <span class="legend-text">已完工</span>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/loaders/GLTFLoader.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <script src="app.js"></script>
</body>
</html>