<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>道路生长动画测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 28px;
            color: #00ff88;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 16px;
            color: #cccccc;
        }
        
        .test-section {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #333;
        }
        
        .section-title {
            font-size: 18px;
            color: #00ff88;
            margin-bottom: 15px;
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-item {
            padding: 8px 0;
            border-bottom: 1px solid #222;
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            margin-right: 10px;
        }
        
        .status.new {
            background: #00ff88;
            color: #000;
        }
        
        .status.enhanced {
            background: #ffaa00;
            color: #000;
        }
        
        .status.existing {
            background: #666;
            color: #fff;
        }
        
        .demo-link {
            display: inline-block;
            background: #00ff88;
            color: #000;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            margin-top: 20px;
            transition: background 0.3s;
        }
        
        .demo-link:hover {
            background: #00cc6a;
        }
        
        .code-block {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #00ff88;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .warning {
            background: rgba(255, 170, 0, 0.1);
            border: 1px solid #ffaa00;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning-title {
            color: #ffaa00;
            font-weight: bold;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">🚧 道路生长动画测试页面</h1>
            <p class="subtitle">验证增强的道路施工进度可视化功能</p>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">🎬 新增功能测试</h2>
            <ul class="feature-list">
                <li class="feature-item">
                    <span class="status new">NEW</span>
                    <strong>几何体生长动画</strong> - 道路按几何体逐步生长而非简单显示/隐藏
                </li>
                <li class="feature-item">
                    <span class="status new">NEW</span>
                    <strong>施工粒子效果</strong> - 施工过程中的粉尘和火花粒子系统
                </li>
                <li class="feature-item">
                    <span class="status new">NEW</span>
                    <strong>施工设备动画</strong> - 跟随施工进度移动的挖掘机模型
                </li>
                <li class="feature-item">
                    <span class="status new">NEW</span>
                    <strong>可变动画速度</strong> - 0.1x 到 3.0x 的动画播放速度控制
                </li>
                <li class="feature-item">
                    <span class="status new">NEW</span>
                    <strong>效果开关控制</strong> - 独立控制各种视觉效果的显示
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">🔧 增强功能测试</h2>
            <ul class="feature-list">
                <li class="feature-item">
                    <span class="status enhanced">ENHANCED</span>
                    <strong>精细道路分段</strong> - 从8段增加到16段，更平滑的生长效果
                </li>
                <li class="feature-item">
                    <span class="status enhanced">ENHANCED</span>
                    <strong>摄像机跟随</strong> - 更智能的视角跟随算法
                </li>
                <li class="feature-item">
                    <span class="status enhanced">ENHANCED</span>
                    <strong>材质系统</strong> - 更丰富的施工状态视觉反馈
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">✅ 保留功能</h2>
            <ul class="feature-list">
                <li class="feature-item">
                    <span class="status existing">EXISTING</span>
                    <strong>时间轴控制</strong> - 24个月工程周期模拟
                </li>
                <li class="feature-item">
                    <span class="status existing">EXISTING</span>
                    <strong>道路高度调节</strong> - 实时调整道路高度位置
                </li>
                <li class="feature-item">
                    <span class="status existing">EXISTING</span>
                    <strong>进度百分比显示</strong> - 基于道路长度的精确进度计算
                </li>
            </ul>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">🧪 测试步骤</h2>
            <ol style="color: #cccccc; line-height: 1.6;">
                <li>打开主页面 (index.html) 并加载GLB模型</li>
                <li>在控制面板中确认所有新增选项都已显示</li>
                <li>测试生长动画开关的效果</li>
                <li>调整动画速度并观察变化</li>
                <li>启用/禁用粒子效果和施工设备</li>
                <li>使用摄像机跟随模式观察施工过程</li>
                <li>在浏览器控制台查看新的调试信息</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h2 class="section-title">💻 控制台测试命令</h2>
            <div class="code-block">
// 检查新功能状态
console.log('生长动画:', roadManager.growthAnimationEnabled);
console.log('粒子效果:', roadManager.showParticles);
console.log('施工设备:', roadManager.showEquipment);
console.log('动画速度:', roadManager.animationSpeed);

// 动态调整参数
roadManager.animationSpeed = 2.0;
roadManager.reinitializeRoadSegments();

// 显示道路段信息
roadManager.showRoadPositions();
            </div>
        </div>
        
        <div class="warning">
            <div class="warning-title">⚠️ 注意事项</div>
            <ul style="margin: 5px 0; padding-left: 20px;">
                <li>确保GLB模型文件存在且可访问</li>
                <li>使用HTTP服务器运行以避免CORS问题</li>
                <li>建议使用支持WebGL的现代浏览器</li>
                <li>启用所有效果可能影响性能，请根据设备调整</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <a href="index.html" class="demo-link">🚀 开始测试演示</a>
        </div>
    </div>
</body>
</html>
